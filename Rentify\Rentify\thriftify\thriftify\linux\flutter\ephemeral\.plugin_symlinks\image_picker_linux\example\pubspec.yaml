name: example
description: Example for image_picker_linux implementation.
publish_to: 'none'
version: 1.0.0

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"

dependencies:
  flutter:
    sdk: flutter
  image_picker_linux:
    # When depending on this package from a real application you should use:
    #   image_picker_linux: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ..
  image_picker_platform_interface: ^2.8.0
  mime: ^2.0.0
  video_player: ^2.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
