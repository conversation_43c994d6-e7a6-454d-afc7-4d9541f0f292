import 'package:flutter_riverpod/flutter_riverpod.dart';

class MockUser {
  final String uid;
  final String email;
  final String displayName;

  MockUser({
    required this.uid,
    required this.email,
    required this.displayName,
  });
}

class MockAuthService {
  static MockUser? _currentUser = MockUser(
    uid: 'mock_user_123',
    email: '<EMAIL>',
    displayName: 'Demo User',
  );

  MockUser? get currentUser => _currentUser;

  Stream<MockUser?> get authStateChanges async* {
    yield _currentUser;
  }

  Future<void> signOut() async {
    _currentUser = null;
  }

  Future<void> signIn() async {
    _currentUser = MockUser(
      uid: 'mock_user_123',
      email: '<EMAIL>',
      displayName: 'Demo User',
    );
  }

  Future<void> updateDisplayName(String newDisplayName) async {
    if (_currentUser != null) {
      _currentUser = MockUser(
        uid: _currentUser!.uid,
        email: _currentUser!.email,
        displayName: newDisplayName,
      );
    }
  }
}

// State notifier for current user
class MockUserNotifier extends StateNotifier<MockUser?> {
  MockUserNotifier() : super(MockAuthService._currentUser);

  void updateUser(MockUser? user) {
    state = user;
  }

  Future<void> updateDisplayName(String newDisplayName) async {
    if (state != null) {
      final updatedUser = MockUser(
        uid: state!.uid,
        email: state!.email,
        displayName: newDisplayName,
      );
      MockAuthService._currentUser = updatedUser;
      state = updatedUser;
    }
  }

  Future<void> signOut() async {
    MockAuthService._currentUser = null;
    state = null;
  }

  Future<void> signIn() async {
    final user = MockUser(
      uid: 'mock_user_123',
      email: '<EMAIL>',
      displayName: 'Demo User',
    );
    MockAuthService._currentUser = user;
    state = user;
  }
}

// Riverpod providers
final mockAuthServiceProvider = Provider<MockAuthService>((ref) => MockAuthService());

final mockCurrentUserProvider = StateNotifierProvider<MockUserNotifier, MockUser?>((ref) {
  return MockUserNotifier();
});

final mockAuthStateProvider = StreamProvider<MockUser?>((ref) {
  return ref.watch(mockAuthServiceProvider).authStateChanges;
});
