import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'firebase_service.dart';

class ImageService {
  final FirebaseStorage _storage = FirebaseService.instance.storage;
  final ImagePicker _picker = ImagePicker();

  // Pick multiple images from gallery (web-compatible version)
  Future<List<XFile>> pickMultipleImages({int maxImages = 5}) async {
    try {
      // For web, we'll pick images one by one since pickMultipleImages might not be available
      final List<XFile> images = [];

      for (int i = 0; i < maxImages; i++) {
        final XFile? image = await _picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1920,
          maxHeight: 1080,
          imageQuality: 85,
        );

        if (image != null) {
          images.add(image);
        } else {
          break; // User cancelled or no more images
        }
      }

      return images;
    } catch (e) {
      throw Exception('Failed to pick images: $e');
    }
  }

  // Pick single image from gallery
  Future<XFile?> pickSingleImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      return image;
    } catch (e) {
      throw Exception('Failed to pick image: $e');
    }
  }

  // Pick image from camera
  Future<XFile?> pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      return image;
    } catch (e) {
      throw Exception('Failed to take photo: $e');
    }
  }

  // Upload image to Firebase Storage
  Future<String> uploadImage({
    required XFile imageFile,
    required String folder,
    String? fileName,
  }) async {
    try {
      final Uint8List imageData = await imageFile.readAsBytes();
      final String finalFileName = fileName ?? 
          '${DateTime.now().millisecondsSinceEpoch}_${imageFile.name}';
      
      final Reference ref = _storage.ref().child('$folder/$finalFileName');
      
      // Set metadata
      final SettableMetadata metadata = SettableMetadata(
        contentType: 'image/jpeg',
        customMetadata: {
          'uploaded_by': 'Rentify_app',
          'upload_time': DateTime.now().toIso8601String(),
        },
      );

      // Upload the file
      final UploadTask uploadTask = ref.putData(imageData, metadata);
      
      // Wait for upload to complete
      final TaskSnapshot snapshot = await uploadTask;
      
      // Get download URL
      final String downloadUrl = await snapshot.ref.getDownloadURL();
      
      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  // Upload multiple images
  Future<List<String>> uploadMultipleImages({
    required List<XFile> imageFiles,
    required String folder,
    String? productId,
  }) async {
    try {
      final List<String> downloadUrls = [];
      
      for (int i = 0; i < imageFiles.length; i++) {
        final XFile imageFile = imageFiles[i];
        final String fileName = productId != null 
            ? '${productId}_image_${i + 1}_${DateTime.now().millisecondsSinceEpoch}.jpg'
            : '${DateTime.now().millisecondsSinceEpoch}_image_${i + 1}.jpg';
        
        final String downloadUrl = await uploadImage(
          imageFile: imageFile,
          folder: folder,
          fileName: fileName,
        );
        
        downloadUrls.add(downloadUrl);
      }
      
      return downloadUrls;
    } catch (e) {
      throw Exception('Failed to upload images: $e');
    }
  }

  // Delete image from Firebase Storage
  Future<void> deleteImage(String imageUrl) async {
    try {
      final Reference ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      throw Exception('Failed to delete image: $e');
    }
  }

  // Delete multiple images
  Future<void> deleteMultipleImages(List<String> imageUrls) async {
    try {
      for (final String imageUrl in imageUrls) {
        await deleteImage(imageUrl);
      }
    } catch (e) {
      throw Exception('Failed to delete images: $e');
    }
  }

  // Get image size in bytes
  Future<int> getImageSize(XFile imageFile) async {
    try {
      final Uint8List imageData = await imageFile.readAsBytes();
      return imageData.length;
    } catch (e) {
      throw Exception('Failed to get image size: $e');
    }
  }

  // Validate image file
  bool isValidImageFile(XFile file) {
    final String fileName = file.name.toLowerCase();
    final List<String> allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    
    return allowedExtensions.any((ext) => fileName.endsWith(ext));
  }

  // Format file size for display
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

// Riverpod provider
final imageServiceProvider = Provider<ImageService>((ref) => ImageService());
